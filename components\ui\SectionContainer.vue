<template>
  <section :id="id" :class="sectionClasses">
    <div :class="containerClasses">
      <div v-if="title || subtitle || $slots.header" class="text-center mb-12">
        <div v-if="$slots.header">
          <slot name="header" />
        </div>
        <template v-else>
          <h2 v-if="title" :class="titleClasses">{{ title }}</h2>
          <p v-if="subtitle" :class="subtitleClasses">{{ subtitle }}</p>
        </template>
      </div>
      
      <div :class="contentClasses">
        <slot />
      </div>
    </div>
  </section>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

interface Props {
  id?: string
  title?: string
  subtitle?: string
  variant?: 'default' | 'primary' | 'secondary' | 'dark'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  containerSize?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  noPadding?: boolean
  centerContent?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  size: 'md',
  containerSize: 'lg',
  noPadding: false,
  centerContent: false
})

const variantClasses = computed(() => {
  const variants = {
    default: 'bg-background',
    primary: 'bg-primary text-secondary',
    secondary: 'bg-secondary text-primary',
    dark: 'bg-primary/95 text-secondary'
  }
  return variants[props.variant]
})

const paddingClasses = computed(() => {
  if (props.noPadding) return ''
  
  const sizes = {
    sm: 'py-12',
    md: 'py-16',
    lg: 'py-20',
    xl: 'py-24'
  }
  return `${sizes[props.size]} px-4 sm:px-6 lg:px-8`
})

const containerSizeClasses = computed(() => {
  const sizes = {
    sm: 'max-w-2xl',
    md: 'max-w-4xl',
    lg: 'max-w-6xl',
    xl: 'max-w-7xl',
    full: 'max-w-full'
  }
  return `${sizes[props.containerSize]} mx-auto`
})

const sectionClasses = computed(() => {
  return [
    variantClasses.value,
    paddingClasses.value
  ].filter(Boolean).join(' ')
})

const containerClasses = computed(() => {
  return containerSizeClasses.value
})

const contentClasses = computed(() => {
  return props.centerContent ? 'flex flex-col items-center' : ''
})

const titleClasses = computed(() => {
  const sizes = {
    sm: 'text-2xl',
    md: 'text-3xl',
    lg: 'text-4xl',
    xl: 'text-5xl'
  }
  return `font-bold mb-4 ${sizes[props.size]}`
})

const subtitleClasses = computed(() => {
  const sizes = {
    sm: 'text-base',
    md: 'text-lg',
    lg: 'text-xl',
    xl: 'text-2xl'
  }
  return `opacity-80 ${sizes[props.size]}`
})
</script>
