<template>
  <div class="bg-background h-full w-full">
    <Navbar />
    <main>
      <slot />
    </main>
  </div>
</template>

<script lang="ts" setup>
import Navbar from '~/components/ui/Navbar.vue';
</script>

<style>
/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

/* Add scroll padding to account for fixed navbar */
section[id] {
  scroll-margin-top: 6rem; /* 96px - adjust based on navbar height */
}

/* For mobile, use smaller offset */
@media (max-width: 768px) {
  section[id] {
    scroll-margin-top: 4rem; /* 64px for mobile navbar */
  }
}
</style>