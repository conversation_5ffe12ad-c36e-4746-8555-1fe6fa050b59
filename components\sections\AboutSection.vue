<template>
  <SectionContainer
    :id="id"
    :title="title"
    :subtitle="subtitle"
    :variant="variant"
    :size="size"
  >
    <div :class="layoutClasses">
      <!-- Content Side -->
      <div class="space-y-6">
        <div v-if="description" class="prose prose-lg max-w-none">
          <p class="text-primary/80 leading-relaxed">{{ description }}</p>
        </div>
        
        <!-- Stats/Highlights -->
        <div v-if="stats && stats.length" class="grid grid-cols-2 sm:grid-cols-4 gap-6 py-8">
          <div
            v-for="(stat, index) in stats"
            :key="index"
            class="text-center"
          >
            <div class="text-3xl sm:text-4xl font-bold text-primary mb-2">
              {{ stat.value }}
            </div>
            <div class="text-sm text-primary/70">{{ stat.label }}</div>
          </div>
        </div>
        
        <!-- Features/Benefits -->
        <div v-if="features && features.length" class="space-y-4">
          <div
            v-for="(feature, index) in features"
            :key="index"
            class="flex items-start space-x-3"
          >
            <div class="flex-shrink-0 w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center mt-1">
              <Icon :name="feature.icon || 'heroicons:check'" class="w-4 h-4 text-primary" />
            </div>
            <div>
              <h4 class="font-semibold text-primary mb-1">{{ feature.title }}</h4>
              <p class="text-primary/70 text-sm">{{ feature.description }}</p>
            </div>
          </div>
        </div>
        
        <!-- Call to Action -->
        <div v-if="cta" class="pt-6">
          <Button
            :variant="cta.variant || 'primary'"
            :size="cta.size || 'lg'"
            :icon="cta.icon"
            :href="cta.href"
            :to="cta.to"
            @click="handleCta"
          >
            {{ cta.text }}
          </Button>
        </div>
      </div>
      
      <!-- Image/Media Side -->
      <div v-if="image || $slots.media" class="relative">
        <slot name="media">
          <div v-if="image" class="relative overflow-hidden rounded-2xl">
            <NuxtImg
              :src="image"
              :alt="imageAlt"
              class="w-full h-full object-cover"
              loading="lazy"
            />
            <div v-if="imageBadge" class="absolute top-4 right-4">
              <span class="bg-primary text-secondary px-3 py-1 rounded-full text-sm font-medium">
                {{ imageBadge }}
              </span>
            </div>
          </div>
        </slot>
      </div>
    </div>
    
    <!-- Team/Owner Section -->
    <div v-if="owner" class="mt-16 pt-12 border-t border-primary/10">
      <div class="flex flex-col md:flex-row items-center gap-8">
        <div v-if="owner.image" class="flex-shrink-0">
          <NuxtImg
            :src="owner.image"
            :alt="owner.name"
            class="w-32 h-32 rounded-full object-cover"
            loading="lazy"
          />
        </div>
        <div class="text-center md:text-left">
          <h3 class="text-2xl font-bold text-primary mb-2">{{ owner.name }}</h3>
          <p class="text-primary/70 mb-4">{{ owner.title }}</p>
          <p class="text-primary/80 leading-relaxed">{{ owner.bio }}</p>
        </div>
      </div>
    </div>
  </SectionContainer>
</template>

<script lang="ts" setup>
import { computed, useSlots } from 'vue'
import Button from '../ui/Button.vue'
import SectionContainer from '../ui/SectionContainer.vue'

interface Stat {
  value: string
  label: string
}

interface Feature {
  icon?: string
  title: string
  description: string
}

interface Cta {
  text: string
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  icon?: string
  href?: string
  to?: string
}

interface Owner {
  name: string
  title: string
  bio: string
  image?: string
}

interface Props {
  id?: string
  title?: string
  subtitle?: string
  description?: string
  image?: string
  imageAlt?: string
  imageBadge?: string
  stats?: Stat[]
  features?: Feature[]
  cta?: Cta
  owner?: Owner
  variant?: 'default' | 'primary' | 'secondary' | 'dark'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  layout?: 'default' | 'reverse'
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  size: 'md',
  layout: 'default',
  imageAlt: 'About us'
})

const emit = defineEmits<{
  cta: []
}>()

const slots = useSlots()

const layoutClasses = computed(() => {
  const baseClasses = 'grid gap-12 items-center'

  if (!props.image && !slots.media) {
    return `${baseClasses} grid-cols-1`
  }
  
  const orderClasses = props.layout === 'reverse' 
    ? 'lg:grid-cols-2 lg:gap-16' 
    : 'lg:grid-cols-2 lg:gap-16'
    
  return `${baseClasses} ${orderClasses}`
})

const handleCta = () => {
  emit('cta')
}
</script>
