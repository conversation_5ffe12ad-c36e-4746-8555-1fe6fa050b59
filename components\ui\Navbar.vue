<template>
  <nav
    class="fixed top-0 left-0 w-full z-50 transition-all duration-300 ease-in-out"
    :class="[
      isScrolled
        ? 'h-14 md:h-20 backdrop-blur-xs bg-gradient-to-b from-black/35 to-transparent'
        : 'h-20 md:h-30'
    ]"
  >
    <div class="flex items-center justify-center h-full px-4">
      <div
        class="transition-all duration-500 ease-in-out"
        :class="[
          isScrolled
            ? 'w-38 md:w-48 h-12'
            : 'w-48 md:w-64 h-12'
        ]"
      >
        <NuxtImg
          src="/logo.png"
          alt="Logo"
          class="w-full h-full object-contain"
          loading="eager"
        />
      </div>
    </div>
  </nav>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'

const isScrolled = ref(false)

const handleScroll = () => {
  isScrolled.value = window.scrollY > 50
}

onMounted(() => {
  window.addEventListener('scroll', handleScroll)
  // Check initial scroll position
  handleScroll()
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
/* Additional custom styles if needed */
</style>