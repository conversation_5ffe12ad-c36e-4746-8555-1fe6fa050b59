<template>
  <SectionContainer
    :id="id"
    :title="title"
    :subtitle="subtitle"
    :variant="variant"
    :size="size"
  >
    <!-- Gallery Grid -->
    <div :class="gridClasses">
      <div
        v-for="(item, index) in displayItems"
        :key="index"
        class="group relative overflow-hidden rounded-xl cursor-pointer"
        :class="getItemClasses(index)"
        @click="openLightbox(index)"
      >
        <NuxtImg
          :src="item.src"
          :alt="item.alt"
          class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
          loading="lazy"
        />
        
        <!-- Overlay -->
        <div class="absolute inset-0 bg-primary/0 group-hover:bg-primary/30 transition-colors duration-300 flex items-center justify-center">
          <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <Icon name="heroicons:magnifying-glass-plus" class="w-8 h-8 text-secondary" />
          </div>
        </div>
        
        <!-- Caption -->
        <div v-if="item.caption" class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-primary/80 to-transparent p-4">
          <p class="text-secondary text-sm font-medium">{{ item.caption }}</p>
        </div>
      </div>
    </div>
    
    <!-- Load More Button -->
    <div v-if="hasMore" class="text-center mt-8">
      <Button
        variant="outline"
        size="lg"
        @click="loadMore"
      >
        Load More Images
      </Button>
    </div>
    
    <!-- Lightbox Modal -->
    <div
      v-if="lightboxOpen"
      class="fixed inset-0 z-50 bg-primary/90 flex items-center justify-center p-4"
      @click="closeLightbox"
    >
      <div class="relative max-w-4xl max-h-full">
        <!-- Close Button -->
        <button
          @click="closeLightbox"
          class="absolute -top-12 right-0 text-secondary hover:text-secondary/80 transition-colors"
        >
          <Icon name="heroicons:x-mark" class="w-8 h-8" />
        </button>
        
        <!-- Navigation -->
        <button
          v-if="currentIndex > 0"
          @click.stop="previousImage"
          class="absolute left-4 top-1/2 transform -translate-y-1/2 text-secondary hover:text-secondary/80 transition-colors"
        >
          <Icon name="heroicons:chevron-left" class="w-8 h-8" />
        </button>
        
        <button
          v-if="currentIndex < items.length - 1"
          @click.stop="nextImage"
          class="absolute right-4 top-1/2 transform -translate-y-1/2 text-secondary hover:text-secondary/80 transition-colors"
        >
          <Icon name="heroicons:chevron-right" class="w-8 h-8" />
        </button>
        
        <!-- Image -->
        <NuxtImg
          :src="items[currentIndex].src"
          :alt="items[currentIndex].alt"
          class="max-w-full max-h-full object-contain rounded-lg"
          @click.stop
        />
        
        <!-- Caption -->
        <div v-if="items[currentIndex].caption" class="absolute bottom-4 left-4 right-4 text-center">
          <p class="text-secondary bg-primary/50 px-4 py-2 rounded-lg backdrop-blur-sm">
            {{ items[currentIndex].caption }}
          </p>
        </div>
        
        <!-- Counter -->
        <div class="absolute top-4 left-4 text-secondary bg-primary/50 px-3 py-1 rounded-lg backdrop-blur-sm text-sm">
          {{ currentIndex + 1 }} / {{ items.length }}
        </div>
      </div>
    </div>
  </SectionContainer>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

interface GalleryItem {
  src: string
  alt: string
  caption?: string
}

interface Props {
  id?: string
  title?: string
  subtitle?: string
  items: GalleryItem[]
  variant?: 'default' | 'primary' | 'secondary' | 'dark'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  layout?: 'grid' | 'masonry'
  columns?: 2 | 3 | 4 | 5
  initialLoad?: number
  loadMoreCount?: number
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  size: 'md',
  layout: 'grid',
  columns: 3,
  initialLoad: 9,
  loadMoreCount: 6
})

const lightboxOpen = ref(false)
const currentIndex = ref(0)
const loadedCount = ref(props.initialLoad)

const displayItems = computed(() => {
  return props.items.slice(0, loadedCount.value)
})

const hasMore = computed(() => {
  return loadedCount.value < props.items.length
})

const gridClasses = computed(() => {
  const columnClasses = {
    2: 'grid-cols-1 sm:grid-cols-2',
    3: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4',
    5: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5'
  }
  
  return `grid ${columnClasses[props.columns]} gap-4`
})

const getItemClasses = (index: number) => {
  // Create varied heights for more interesting layout
  const heights = ['h-64', 'h-80', 'h-72', 'h-96', 'h-64', 'h-88']
  return heights[index % heights.length]
}

const openLightbox = (index: number) => {
  currentIndex.value = index
  lightboxOpen.value = true
  document.body.style.overflow = 'hidden'
}

const closeLightbox = () => {
  lightboxOpen.value = false
  document.body.style.overflow = 'auto'
}

const nextImage = () => {
  if (currentIndex.value < props.items.length - 1) {
    currentIndex.value++
  }
}

const previousImage = () => {
  if (currentIndex.value > 0) {
    currentIndex.value--
  }
}

const loadMore = () => {
  loadedCount.value = Math.min(
    loadedCount.value + props.loadMoreCount,
    props.items.length
  )
}

const handleKeydown = (event: KeyboardEvent) => {
  if (!lightboxOpen.value) return
  
  switch (event.key) {
    case 'Escape':
      closeLightbox()
      break
    case 'ArrowLeft':
      previousImage()
      break
    case 'ArrowRight':
      nextImage()
      break
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  document.body.style.overflow = 'auto'
})
</script>
