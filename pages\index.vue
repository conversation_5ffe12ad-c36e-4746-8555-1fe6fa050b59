<template>
  <div>
    <!-- Hero Section -->
    <section class="relative min-h-screen flex items-center justify-center overflow-hidden">
      <!-- Background -->
      <div class="absolute inset-0 z-0">
        <div class="bg-gradient-to-br from-primary/10 via-background to-secondary/20"></div>
      </div>

      <!-- Content -->
      <div class="relative z-10 text-center px-4 sm:px-6 lg:px-8 max-w-5xl mx-auto">
        <div class="space-y-8">
          <!-- Main Heading -->
          <div class="space-y-4">
            <h1 class="text-4xl sm:text-5xl lg:text-7xl font-bold text-primary leading-tight">
              {{ (businessData as any)?.hero?.title || 'Your Business Name' }}
            </h1>
            <p class="text-xl sm:text-2xl lg:text-3xl text-primary/80 max-w-3xl mx-auto">
              {{ (businessData as any)?.hero?.subtitle || 'Professional Services You Can Trust' }}
            </p>
          </div>

          <!-- Description -->
          <p class="text-lg sm:text-xl text-primary/70 max-w-2xl mx-auto leading-relaxed">
            {{ (businessData as any)?.hero?.description || 'We provide exceptional services tailored to your needs. Experience quality, reliability, and excellence in everything we do.' }}
          </p>

          <!-- Call to Action Buttons -->
          <div class="flex flex-col sm:flex-row gap-4 justify-center items-center pt-8">
            <a
              href="#contact"
              class="inline-flex items-center justify-center px-8 py-4 text-xl font-medium bg-primary text-secondary rounded-xl hover:bg-primary/90 transition-colors duration-200 gap-3"
            >
              <Icon name="heroicons:arrow-right" class="w-7 h-7" />
              Get Started
            </a>

            <a
              href="#about"
              class="inline-flex items-center justify-center px-8 py-4 text-xl font-medium border-2 border-primary text-primary rounded-xl hover:bg-primary hover:text-secondary transition-colors duration-200 gap-3"
            >
              <Icon name="heroicons:information-circle" class="w-7 h-7" />
              Learn More
            </a>
          </div>

          <!-- Features/Highlights -->
          <div class="pt-12">
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <div
                v-for="(feature, index) in (businessData as any)?.hero?.features || [
                  { icon: 'heroicons:star', title: 'Quality Service', description: 'Top-notch quality in everything we do' },
                  { icon: 'heroicons:clock', title: 'Fast Delivery', description: 'Quick turnaround times' },
                  { icon: 'heroicons:shield-check', title: 'Trusted', description: 'Reliable and trustworthy service' }
                ]"
                :key="index"
                class="flex flex-col items-center text-center space-y-3"
              >
                <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                  <Icon :name="feature.icon" class="w-8 h-8 text-primary" />
                </div>
                <h3 class="text-lg font-semibold text-primary">{{ feature.title }}</h3>
                <p class="text-primary/70 text-sm">{{ feature.description }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Scroll Indicator -->
      <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <Icon name="heroicons:chevron-down" class="w-6 h-6 text-primary/60" />
      </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-20 px-4 sm:px-6 lg:px-8">
      <div class="max-w-6xl mx-auto">
        <div class="text-center mb-12">
          <h2 class="text-3xl sm:text-4xl font-bold text-primary mb-4">{{ (businessData as any)?.about?.title || 'About Us' }}</h2>
          <p class="text-lg text-primary/80">{{ (businessData as any)?.about?.subtitle || 'Your trusted partner for exceptional service' }}</p>
        </div>

        <div class="grid lg:grid-cols-2 gap-12 items-center mb-16">
          <!-- Content Side -->
          <div class="space-y-6">
            <p class="text-primary/80 leading-relaxed text-lg">
              With years of experience in the industry, we have built a reputation for delivering outstanding results. Our commitment to quality and customer satisfaction sets us apart from the competition.
            </p>

            <!-- Features/Benefits -->
            <div class="space-y-4">
              <div class="flex items-start space-x-3">
                <div class="flex-shrink-0 w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center mt-1">
                  <Icon name="heroicons:check-circle" class="w-4 h-4 text-primary" />
                </div>
                <div>
                  <h4 class="font-semibold text-primary mb-1">Professional Team</h4>
                  <p class="text-primary/70 text-sm">Experienced professionals dedicated to your success</p>
                </div>
              </div>
              <div class="flex items-start space-x-3">
                <div class="flex-shrink-0 w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center mt-1">
                  <Icon name="heroicons:check-circle" class="w-4 h-4 text-primary" />
                </div>
                <div>
                  <h4 class="font-semibold text-primary mb-1">Quality Guarantee</h4>
                  <p class="text-primary/70 text-sm">We stand behind our work with a satisfaction guarantee</p>
                </div>
              </div>
              <div class="flex items-start space-x-3">
                <div class="flex-shrink-0 w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center mt-1">
                  <Icon name="heroicons:check-circle" class="w-4 h-4 text-primary" />
                </div>
                <div>
                  <h4 class="font-semibold text-primary mb-1">Competitive Pricing</h4>
                  <p class="text-primary/70 text-sm">Fair and transparent pricing for all our services</p>
                </div>
              </div>
            </div>

            <!-- Call to Action -->
            <div class="pt-6">
              <a
                href="#contact"
                class="inline-flex items-center justify-center px-6 py-3 text-lg font-medium bg-primary text-secondary rounded-lg hover:bg-primary/90 transition-colors duration-200 gap-2"
              >
                <Icon name="heroicons:phone" class="w-6 h-6" />
                Contact Us Today
              </a>
            </div>
          </div>

          <!-- Stats Side -->
          <div class="space-y-8">
            <!-- Stats -->
            <div class="grid grid-cols-2 gap-6">
              <div class="text-center bg-secondary/20 p-6 rounded-xl">
                <div class="text-3xl sm:text-4xl font-bold text-primary mb-2">500+</div>
                <div class="text-sm text-primary/70">Happy Clients</div>
              </div>
              <div class="text-center bg-secondary/20 p-6 rounded-xl">
                <div class="text-3xl sm:text-4xl font-bold text-primary mb-2">5+</div>
                <div class="text-sm text-primary/70">Years Experience</div>
              </div>
              <div class="text-center bg-secondary/20 p-6 rounded-xl">
                <div class="text-3xl sm:text-4xl font-bold text-primary mb-2">1000+</div>
                <div class="text-sm text-primary/70">Projects Completed</div>
              </div>
              <div class="text-center bg-secondary/20 p-6 rounded-xl">
                <div class="text-3xl sm:text-4xl font-bold text-primary mb-2">24/7</div>
                <div class="text-sm text-primary/70">Support</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Owner Section -->
        <div class="pt-12 border-t border-primary/10">
          <div class="flex flex-col md:flex-row items-center gap-8 max-w-4xl mx-auto">
            <div class="flex-shrink-0">
              <div class="w-32 h-32 bg-primary/10 rounded-full flex items-center justify-center">
                <Icon name="heroicons:user" class="w-16 h-16 text-primary/60" />
              </div>
            </div>
            <div class="text-center md:text-left">
              <h3 class="text-2xl font-bold text-primary mb-2">John Doe</h3>
              <p class="text-primary/70 mb-4">Founder & CEO</p>
              <p class="text-primary/80 leading-relaxed">
                With over 10 years of experience in the industry, John founded this company with a vision to provide exceptional service to every client.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-20 px-4 sm:px-6 lg:px-8 bg-secondary/20">
      <div class="max-w-6xl mx-auto">
        <div class="text-center mb-12">
          <h2 class="text-3xl sm:text-4xl font-bold text-primary mb-4">{{ (businessData as any)?.services?.title || 'Our Services' }}</h2>
          <p class="text-lg text-primary/80">{{ (businessData as any)?.services?.subtitle || 'What we offer to help you succeed' }}</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
          <!-- Service cards from content -->
          <div
            v-for="(service, index) in servicesData"
            :key="index"
            class="bg-secondary shadow-lg rounded-xl overflow-hidden hover:scale-105 transition-transform duration-300 flex flex-col h-full"
          >
            <div class="p-6 flex flex-col flex-1">
              <div class="mb-3">
                <div class="flex items-center justify-between mb-2">
                  <h3 class="text-xl font-bold text-primary">{{ service.title }}</h3>
                  <span v-if="service.badge" class="bg-primary text-secondary px-3 py-1 rounded-full text-sm font-medium">
                    {{ service.badge }}
                  </span>
                </div>
              </div>

              <div class="mb-4">
                <p class="text-primary/80 leading-relaxed">{{ service.description }}</p>
              </div>

              <div v-if="service.price" class="mb-4">
                <span class="text-2xl font-bold text-primary">{{ service.price }}</span>
                <span v-if="service.priceUnit" class="text-primary/60 ml-1">{{ service.priceUnit }}</span>
              </div>

              <ul v-if="service.features" class="space-y-2 mb-4">
                <li
                  v-for="(feature, featureIndex) in service.features"
                  :key="featureIndex"
                  class="flex items-center text-sm text-primary/80"
                >
                  <Icon name="heroicons:check" class="w-4 h-4 text-green-600 mr-2 flex-shrink-0" />
                  {{ feature }}
                </li>
              </ul>

              <div class="mt-auto">
                <div class="flex flex-col gap-2">
                  <a
                    v-if="service.primaryAction"
                    href="#contact"
                    class="w-full inline-flex items-center justify-center px-4 py-2 text-base font-medium bg-primary text-secondary rounded-lg hover:bg-primary/90 transition-colors duration-200 gap-2"
                    @click="handleServiceAction(service, 'primary')"
                  >
                    <Icon :name="service.primaryAction.icon || 'heroicons:arrow-right'" class="w-5 h-5" />
                    {{ service.primaryAction.text }}
                  </a>

                  <a
                    v-if="service.secondaryAction"
                    href="#about"
                    class="w-full inline-flex items-center justify-center px-4 py-2 text-base font-medium border-2 border-primary text-primary rounded-lg hover:bg-primary hover:text-secondary transition-colors duration-200 gap-2"
                    @click="handleServiceAction(service, 'secondary')"
                  >
                    <Icon :name="service.secondaryAction.icon || 'heroicons:information-circle'" class="w-5 h-5" />
                    {{ service.secondaryAction.text }}
                  </a>
                </div>
              </div>
            </div>
          </div>

          <!-- Fallback services if no content -->
          <template v-if="!servicesData || servicesData.length === 0">
            <!-- Service 1 -->
          <div class="bg-secondary shadow-lg rounded-xl overflow-hidden hover:scale-105 transition-transform duration-300 flex flex-col h-full">
            <div class="p-6 flex flex-col flex-1">
              <div class="mb-3">
                <div class="flex items-center justify-between mb-2">
                  <h3 class="text-xl font-bold text-primary">Service One</h3>
                  <span class="bg-primary text-secondary px-3 py-1 rounded-full text-sm font-medium">Popular</span>
                </div>
              </div>

              <div class="mb-4">
                <p class="text-primary/80 leading-relaxed">Comprehensive service description that explains the value and benefits to your customers.</p>
              </div>

              <div class="mb-4">
                <span class="text-2xl font-bold text-primary">$99</span>
                <span class="text-primary/60 ml-1">/month</span>
              </div>

              <ul class="space-y-2 mb-4">
                <li class="flex items-center text-sm text-primary/80">
                  <Icon name="heroicons:check" class="w-4 h-4 text-green-600 mr-2 flex-shrink-0" />
                  Feature 1 included
                </li>
                <li class="flex items-center text-sm text-primary/80">
                  <Icon name="heroicons:check" class="w-4 h-4 text-green-600 mr-2 flex-shrink-0" />
                  Feature 2 included
                </li>
                <li class="flex items-center text-sm text-primary/80">
                  <Icon name="heroicons:check" class="w-4 h-4 text-green-600 mr-2 flex-shrink-0" />
                  24/7 Support
                </li>
                <li class="flex items-center text-sm text-primary/80">
                  <Icon name="heroicons:check" class="w-4 h-4 text-green-600 mr-2 flex-shrink-0" />
                  Money-back guarantee
                </li>
              </ul>

              <div class="mt-auto">
                <div class="flex flex-col gap-2">
                  <a
                    href="#contact"
                    class="w-full inline-flex items-center justify-center px-4 py-2 text-base font-medium bg-primary text-secondary rounded-lg hover:bg-primary/90 transition-colors duration-200 gap-2"
                  >
                    <Icon name="heroicons:arrow-right" class="w-5 h-5" />
                    Get Started
                  </a>

                  <a
                    href="#about"
                    class="w-full inline-flex items-center justify-center px-4 py-2 text-base font-medium border-2 border-primary text-primary rounded-lg hover:bg-primary hover:text-secondary transition-colors duration-200 gap-2"
                  >
                    <Icon name="heroicons:information-circle" class="w-5 h-5" />
                    Learn More
                  </a>
                </div>
              </div>
            </div>
          </div>

          <!-- Service 2 -->
          <div class="bg-secondary shadow-lg rounded-xl overflow-hidden hover:scale-105 transition-transform duration-300 flex flex-col h-full">
            <div class="p-6 flex flex-col flex-1">
              <div class="mb-3">
                <h3 class="text-xl font-bold text-primary">Service Two</h3>
              </div>

              <div class="mb-4">
                <p class="text-primary/80 leading-relaxed">Another excellent service that provides great value to your customers with professional results.</p>
              </div>

              <div class="mb-4">
                <span class="text-2xl font-bold text-primary">$149</span>
                <span class="text-primary/60 ml-1">/month</span>
              </div>

              <ul class="space-y-2 mb-4">
                <li class="flex items-center text-sm text-primary/80">
                  <Icon name="heroicons:check" class="w-4 h-4 text-green-600 mr-2 flex-shrink-0" />
                  Everything in Service One
                </li>
                <li class="flex items-center text-sm text-primary/80">
                  <Icon name="heroicons:check" class="w-4 h-4 text-green-600 mr-2 flex-shrink-0" />
                  Advanced features
                </li>
                <li class="flex items-center text-sm text-primary/80">
                  <Icon name="heroicons:check" class="w-4 h-4 text-green-600 mr-2 flex-shrink-0" />
                  Priority support
                </li>
                <li class="flex items-center text-sm text-primary/80">
                  <Icon name="heroicons:check" class="w-4 h-4 text-green-600 mr-2 flex-shrink-0" />
                  Custom solutions
                </li>
              </ul>

              <div class="mt-auto">
                <a
                  href="#contact"
                  class="w-full inline-flex items-center justify-center px-4 py-2 text-base font-medium bg-primary text-secondary rounded-lg hover:bg-primary/90 transition-colors duration-200 gap-2"
                >
                  <Icon name="heroicons:arrow-right" class="w-5 h-5" />
                  Get Started
                </a>
              </div>
            </div>
          </div>

          <!-- Service 3 -->
          <div class="bg-secondary shadow-lg rounded-xl overflow-hidden hover:scale-105 transition-transform duration-300 flex flex-col h-full">
            <div class="p-6 flex flex-col flex-1">
              <div class="mb-3">
                <div class="flex items-center justify-between mb-2">
                  <h3 class="text-xl font-bold text-primary">Service Three</h3>
                  <span class="bg-primary text-secondary px-3 py-1 rounded-full text-sm font-medium">Premium</span>
                </div>
              </div>

              <div class="mb-4">
                <p class="text-primary/80 leading-relaxed">Premium service offering with all the bells and whistles for demanding customers.</p>
              </div>

              <div class="mb-4">
                <span class="text-2xl font-bold text-primary">$199</span>
                <span class="text-primary/60 ml-1">/month</span>
              </div>

              <ul class="space-y-2 mb-4">
                <li class="flex items-center text-sm text-primary/80">
                  <Icon name="heroicons:check" class="w-4 h-4 text-green-600 mr-2 flex-shrink-0" />
                  Everything included
                </li>
                <li class="flex items-center text-sm text-primary/80">
                  <Icon name="heroicons:check" class="w-4 h-4 text-green-600 mr-2 flex-shrink-0" />
                  White-glove service
                </li>
                <li class="flex items-center text-sm text-primary/80">
                  <Icon name="heroicons:check" class="w-4 h-4 text-green-600 mr-2 flex-shrink-0" />
                  Dedicated account manager
                </li>
                <li class="flex items-center text-sm text-primary/80">
                  <Icon name="heroicons:check" class="w-4 h-4 text-green-600 mr-2 flex-shrink-0" />
                  Custom integrations
                </li>
              </ul>

              <div class="mt-auto">
                <a
                  href="#contact"
                  class="w-full inline-flex items-center justify-center px-4 py-2 text-base font-medium bg-primary text-secondary rounded-lg hover:bg-primary/90 transition-colors duration-200 gap-2"
                >
                  <Icon name="heroicons:phone" class="w-5 h-5" />
                  Contact Us
                </a>
              </div>
            </div>
          </div>
          </template>
        </div>
      </div>
    </section>

    <!-- Gallery Section -->
    <section id="gallery" class="py-20 px-4 sm:px-6 lg:px-8">
      <div class="max-w-6xl mx-auto">
        <div class="text-center mb-12">
          <h2 class="text-3xl sm:text-4xl font-bold text-primary mb-4">{{ (businessData as any)?.gallery?.title || 'Our Work' }}</h2>
          <p class="text-lg text-primary/80">{{ (businessData as any)?.gallery?.subtitle || 'See what we\'ve accomplished for our clients' }}</p>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          <div class="group relative overflow-hidden rounded-xl cursor-pointer h-64">
            <div class="w-full h-full bg-gradient-to-br from-primary/20 to-secondary/40 flex items-center justify-center">
              <div class="text-center">
                <Icon name="heroicons:photo" class="w-12 h-12 text-primary/60 mx-auto mb-2" />
                <p class="text-primary/60 text-sm">Beautiful project completed for client</p>
              </div>
            </div>
            <div class="absolute inset-0 bg-primary/0 group-hover:bg-primary/30 transition-colors duration-300 flex items-center justify-center">
              <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <Icon name="heroicons:magnifying-glass-plus" class="w-8 h-8 text-secondary" />
              </div>
            </div>
          </div>

          <div class="group relative overflow-hidden rounded-xl cursor-pointer h-64">
            <div class="w-full h-full bg-gradient-to-br from-secondary/40 to-primary/20 flex items-center justify-center">
              <div class="text-center">
                <Icon name="heroicons:photo" class="w-12 h-12 text-primary/60 mx-auto mb-2" />
                <p class="text-primary/60 text-sm">Another successful project</p>
              </div>
            </div>
            <div class="absolute inset-0 bg-primary/0 group-hover:bg-primary/30 transition-colors duration-300 flex items-center justify-center">
              <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <Icon name="heroicons:magnifying-glass-plus" class="w-8 h-8 text-secondary" />
              </div>
            </div>
          </div>

          <div class="group relative overflow-hidden rounded-xl cursor-pointer h-64">
            <div class="w-full h-full bg-gradient-to-br from-primary/30 to-secondary/30 flex items-center justify-center">
              <div class="text-center">
                <Icon name="heroicons:photo" class="w-12 h-12 text-primary/60 mx-auto mb-2" />
                <p class="text-primary/60 text-sm">Quality work delivered on time</p>
              </div>
            </div>
            <div class="absolute inset-0 bg-primary/0 group-hover:bg-primary/30 transition-colors duration-300 flex items-center justify-center">
              <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <Icon name="heroicons:magnifying-glass-plus" class="w-8 h-8 text-secondary" />
              </div>
            </div>
          </div>

          <div class="group relative overflow-hidden rounded-xl cursor-pointer h-64">
            <div class="w-full h-full bg-gradient-to-br from-secondary/30 to-primary/40 flex items-center justify-center">
              <div class="text-center">
                <Icon name="heroicons:photo" class="w-12 h-12 text-primary/60 mx-auto mb-2" />
                <p class="text-primary/60 text-sm">Professional results</p>
              </div>
            </div>
            <div class="absolute inset-0 bg-primary/0 group-hover:bg-primary/30 transition-colors duration-300 flex items-center justify-center">
              <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <Icon name="heroicons:magnifying-glass-plus" class="w-8 h-8 text-secondary" />
              </div>
            </div>
          </div>

          <div class="group relative overflow-hidden rounded-xl cursor-pointer h-64">
            <div class="w-full h-full bg-gradient-to-br from-primary/25 to-secondary/35 flex items-center justify-center">
              <div class="text-center">
                <Icon name="heroicons:photo" class="w-12 h-12 text-primary/60 mx-auto mb-2" />
                <p class="text-primary/60 text-sm">Satisfied customer</p>
              </div>
            </div>
            <div class="absolute inset-0 bg-primary/0 group-hover:bg-primary/30 transition-colors duration-300 flex items-center justify-center">
              <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <Icon name="heroicons:magnifying-glass-plus" class="w-8 h-8 text-secondary" />
              </div>
            </div>
          </div>

          <div class="group relative overflow-hidden rounded-xl cursor-pointer h-64">
            <div class="w-full h-full bg-gradient-to-br from-secondary/25 to-primary/35 flex items-center justify-center">
              <div class="text-center">
                <Icon name="heroicons:photo" class="w-12 h-12 text-primary/60 mx-auto mb-2" />
                <p class="text-primary/60 text-sm">Excellence in every detail</p>
              </div>
            </div>
            <div class="absolute inset-0 bg-primary/0 group-hover:bg-primary/30 transition-colors duration-300 flex items-center justify-center">
              <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <Icon name="heroicons:magnifying-glass-plus" class="w-8 h-8 text-secondary" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 px-4 sm:px-6 lg:px-8 bg-primary/95 text-secondary">
      <div class="max-w-6xl mx-auto">
        <div class="text-center mb-12">
          <h2 class="text-3xl sm:text-4xl font-bold mb-4">{{ (businessData as any)?.contact?.title || 'Get In Touch' }}</h2>
          <p class="text-lg opacity-80">{{ (businessData as any)?.contact?.subtitle || 'Ready to start your project? Contact us today!' }}</p>
        </div>

        <div class="grid lg:grid-cols-2 gap-12">
          <!-- Contact Information -->
          <div class="space-y-8">
            <div class="space-y-6">
              <!-- Address -->
              <div class="flex items-start space-x-4">
                <div class="flex-shrink-0 w-12 h-12 bg-secondary/20 rounded-lg flex items-center justify-center">
                  <Icon name="heroicons:map-pin" class="w-6 h-6 text-secondary" />
                </div>
                <div>
                  <h3 class="font-semibold mb-1">Address</h3>
                  <p class="opacity-80">{{ (businessData as any)?.contact?.address || '123 Business Street, City, State 12345' }}</p>
                </div>
              </div>

              <!-- Phone -->
              <div class="flex items-start space-x-4">
                <div class="flex-shrink-0 w-12 h-12 bg-secondary/20 rounded-lg flex items-center justify-center">
                  <Icon name="heroicons:phone" class="w-6 h-6 text-secondary" />
                </div>
                <div>
                  <h3 class="font-semibold mb-1">Phone</h3>
                  <a :href="`tel:${(businessData as any)?.contact?.phone || '+****************'}`" class="opacity-80 hover:opacity-100 transition-opacity">
                    {{ (businessData as any)?.contact?.phone || '+****************' }}
                  </a>
                </div>
              </div>

              <!-- Email -->
              <div class="flex items-start space-x-4">
                <div class="flex-shrink-0 w-12 h-12 bg-secondary/20 rounded-lg flex items-center justify-center">
                  <Icon name="heroicons:envelope" class="w-6 h-6 text-secondary" />
                </div>
                <div>
                  <h3 class="font-semibold mb-1">Email</h3>
                  <a :href="`mailto:${(businessData as any)?.contact?.email || '<EMAIL>'}`" class="opacity-80 hover:opacity-100 transition-opacity">
                    {{ (businessData as any)?.contact?.email || '<EMAIL>' }}
                  </a>
                </div>
              </div>

              <!-- Hours -->
              <div class="flex items-start space-x-4">
                <div class="flex-shrink-0 w-12 h-12 bg-secondary/20 rounded-lg flex items-center justify-center">
                  <Icon name="heroicons:clock" class="w-6 h-6 text-secondary" />
                </div>
                <div>
                  <h3 class="font-semibold mb-2">Hours</h3>
                  <div class="space-y-1">
                    <div
                      v-for="(hour, index) in (businessData as any)?.contact?.hours || [
                        { day: 'Monday - Friday', time: '9:00 AM - 6:00 PM' },
                        { day: 'Saturday', time: '10:00 AM - 4:00 PM' },
                        { day: 'Sunday', time: 'Closed' }
                      ]"
                      :key="index"
                      class="flex justify-between space-x-8 text-sm"
                    >
                      <span class="opacity-80">{{ hour.day }}</span>
                      <span class="opacity-80">{{ hour.time }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Social Links -->
            <div class="pt-6 border-t border-secondary/20">
              <h3 class="font-semibold mb-4">Follow Us</h3>
              <div class="flex space-x-4">
                <a
                  v-for="(social, index) in (businessData as any)?.contact?.socialLinks || [
                    { icon: 'simple-icons:facebook', url: 'https://facebook.com/yourbusiness' },
                    { icon: 'simple-icons:instagram', url: 'https://instagram.com/yourbusiness' },
                    { icon: 'simple-icons:twitter', url: 'https://twitter.com/yourbusiness' }
                  ]"
                  :key="index"
                  :href="social.url"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="w-10 h-10 bg-secondary/20 rounded-lg flex items-center justify-center hover:bg-secondary hover:text-primary transition-colors"
                >
                  <Icon :name="social.icon" class="w-5 h-5" />
                </a>
              </div>
            </div>
          </div>

          <!-- Contact Form -->
          <div class="space-y-6">
            <Card variant="elevated" size="lg">
              <template #title>
                <h3 class="text-xl font-bold text-primary">Send us a message</h3>
              </template>

              <template #content>
                <form @submit.prevent="handleContactForm" class="space-y-4">
                  <div class="grid md:grid-cols-2 gap-4">
                    <div>
                      <label class="block text-sm font-medium text-primary mb-2">Name</label>
                      <input
                        v-model="contactForm.name"
                        type="text"
                        required
                        class="w-full px-4 py-2 border border-primary/20 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary/50 bg-secondary"
                      >
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-primary mb-2">Phone</label>
                      <input
                        v-model="contactForm.phone"
                        type="tel"
                        class="w-full px-4 py-2 border border-primary/20 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary/50 bg-secondary"
                      >
                    </div>
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-primary mb-2">Email</label>
                    <input
                      v-model="contactForm.email"
                      type="email"
                      required
                      class="w-full px-4 py-2 border border-primary/20 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary/50 bg-secondary"
                    >
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-primary mb-2">Message</label>
                    <textarea
                      v-model="contactForm.message"
                      rows="4"
                      required
                      class="w-full px-4 py-2 border border-primary/20 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary/50 bg-secondary resize-none"
                    ></textarea>
                  </div>

                  <Button type="submit" variant="primary" size="lg" full-width>
                    Send Message
                  </Button>
                </form>
              </template>
            </Card>
          </div>
        </div>
      </div>
    </section>

    <!-- Floating Action Buttons -->
    <ClientOnly>
      <FloatingActions
        :whatsapp-number="(businessData as any)?.contact?.whatsapp || '+1555123456'"
        :phone-number="(businessData as any)?.contact?.phone || '+****************'"
        :email="(businessData as any)?.contact?.email || '<EMAIL>'"
        whatsapp-message="Hello! I'm interested in your services. Can you help me?"
      />
    </ClientOnly>
  </div>
</template>

<script setup lang="ts">
// Fetch content from Nuxt Content using new v3.6 syntax
const { data: businessConfig } = await useAsyncData('business-config', () =>
  queryCollection('content').path('/business-config').first()
)

const { data: services } = await useAsyncData('services', () =>
  queryCollection('content').path('/services').all()
)

// Use the content data with fallbacks
const businessData = computed(() => businessConfig.value || {})
const servicesData = computed(() => (services.value || []).sort((a: any, b: any) => (a.order || 0) - (b.order || 0)))

// Utility functions
const scrollToSection = (sectionId: string) => {
  if (typeof window !== 'undefined') {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }
}

const openGalleryItem = (index: number) => {
  console.log(`Gallery item ${index} clicked`)
  // Add your gallery lightbox logic here
}

// Event handlers
const handleServiceAction = (service: any, actionType: 'primary' | 'secondary') => {
  console.log(`Service action clicked:`, service.title, actionType)
  if (actionType === 'primary') {
    scrollToSection('contact')
  }
}

// Contact form
const contactForm = ref({
  name: '',
  phone: '',
  email: '',
  message: ''
})

const handleContactForm = () => {
  console.log('Contact form submitted:', contactForm.value)
  alert('Thank you for your message! We will get back to you soon.')

  // Reset form
  contactForm.value = {
    name: '',
    phone: '',
    email: '',
    message: ''
  }
}

// SEO
useSeoMeta({
  title: computed(() => (businessData.value as any)?.seo?.title || 'Your Business Name - Professional Services'),
  description: computed(() => (businessData.value as any)?.seo?.description || 'Professional services you can trust.'),
  ogTitle: computed(() => (businessData.value as any)?.seo?.ogTitle || 'Your Business Name - Professional Services'),
  ogDescription: computed(() => (businessData.value as any)?.seo?.ogDescription || 'Professional services you can trust.'),
  ogType: 'website'
})
</script>
