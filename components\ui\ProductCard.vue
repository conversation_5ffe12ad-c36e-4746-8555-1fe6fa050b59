<template>
  <Card
    :variant="variant"
    :size="size"
    :image-url="imageUrl"
    :image-alt="imageAlt"
    :badge="badge"
    :hover="hover"
    :clickable="clickable"
    @click="handleClick"
  >
    <template #title>
      <h3 :class="titleClasses">{{ name }}</h3>
    </template>
    
    <template #description>
      <p class="text-primary/80 leading-relaxed">{{ description }}</p>
    </template>
    
    <template #content>
      <!-- Price -->
      <div v-if="price" class="mb-4">
        <span class="text-2xl font-bold text-primary">{{ formattedPrice }}</span>
        <span v-if="originalPrice" class="text-lg text-primary/50 line-through ml-2">{{ formattedOriginalPrice }}</span>
        <span v-if="priceUnit" class="text-primary/60 ml-1">{{ priceUnit }}</span>
      </div>
      
      <!-- Features/Specifications -->
      <ul v-if="features && features.length" class="space-y-2 mb-4">
        <li
          v-for="(feature, index) in features"
          :key="index"
          class="flex items-center text-sm text-primary/80"
        >
          <Icon name="heroicons:check" class="w-4 h-4 text-green-600 mr-2 flex-shrink-0" />
          {{ feature }}
        </li>
      </ul>
      
      <!-- Stock Status -->
      <div v-if="showStock" class="mb-4">
        <span
          :class="stockClasses"
          class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
        >
          <Icon :name="stockIcon" class="w-3 h-3 mr-1" />
          {{ stockText }}
        </span>
      </div>
      
      <!-- Rating -->
      <div v-if="rating" class="flex items-center mb-4">
        <div class="flex items-center">
          <Icon
            v-for="star in 5"
            :key="star"
            name="heroicons:star"
            :class="star <= rating ? 'text-yellow-400' : 'text-gray-300'"
            class="w-4 h-4"
          />
        </div>
        <span class="text-sm text-primary/60 ml-2">({{ reviewCount || 0 }} reviews)</span>
      </div>
    </template>
    
    <template #actions>
      <div class="flex flex-col gap-2">
        <!-- Primary Action -->
        <Button
          v-if="primaryAction"
          :variant="primaryAction.variant || 'primary'"
          :size="actionButtonSize"
          :icon="primaryAction.icon"
          :href="primaryAction.href"
          :to="primaryAction.to"
          full-width
          @click="handlePrimaryAction"
        >
          {{ primaryAction.text }}
        </Button>
        
        <!-- Secondary Action -->
        <Button
          v-if="secondaryAction"
          :variant="secondaryAction.variant || 'outline'"
          :size="actionButtonSize"
          :icon="secondaryAction.icon"
          :href="secondaryAction.href"
          :to="secondaryAction.to"
          full-width
          @click="handleSecondaryAction"
        >
          {{ secondaryAction.text }}
        </Button>
        
        <!-- Quick Actions -->
        <div v-if="showQuickActions" class="flex gap-2 pt-2">
          <Button
            variant="ghost"
            size="sm"
            icon="heroicons:heart"
            @click="handleWishlist"
          />
          <Button
            variant="ghost"
            size="sm"
            icon="heroicons:share"
            @click="handleShare"
          />
          <Button
            v-if="compareEnabled"
            variant="ghost"
            size="sm"
            icon="heroicons:scale"
            @click="handleCompare"
          />
        </div>
      </div>
    </template>
  </Card>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

interface ProductAction {
  text: string
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  icon?: string
  href?: string
  to?: string
}

interface Props {
  // Basic product info
  name: string
  description?: string
  imageUrl?: string
  imageAlt?: string
  
  // Pricing
  price?: number | string
  originalPrice?: number | string
  priceUnit?: string
  currency?: string
  
  // Product details
  features?: string[]
  badge?: string
  
  // Stock & availability
  inStock?: boolean
  stockCount?: number
  showStock?: boolean
  
  // Rating & reviews
  rating?: number
  reviewCount?: number
  
  // Actions
  primaryAction?: ProductAction
  secondaryAction?: ProductAction
  showQuickActions?: boolean
  compareEnabled?: boolean
  
  // Card styling
  variant?: 'default' | 'elevated' | 'outlined' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  hover?: boolean
  clickable?: boolean
  actionButtonSize?: 'sm' | 'md' | 'lg'
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'elevated',
  size: 'md',
  hover: true,
  clickable: false,
  currency: '$',
  inStock: true,
  showStock: false,
  showQuickActions: false,
  compareEnabled: false,
  actionButtonSize: 'md',
  imageAlt: 'Product image'
})

const emit = defineEmits<{
  click: []
  primaryAction: []
  secondaryAction: []
  wishlist: []
  share: []
  compare: []
}>()

// Computed properties
const titleClasses = computed(() => {
  const sizeClasses = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl'
  }
  return `font-bold text-primary ${sizeClasses[props.size]}`
})

const formattedPrice = computed(() => {
  if (typeof props.price === 'number') {
    return `${props.currency}${props.price.toFixed(2)}`
  }
  return props.price
})

const formattedOriginalPrice = computed(() => {
  if (typeof props.originalPrice === 'number') {
    return `${props.currency}${props.originalPrice.toFixed(2)}`
  }
  return props.originalPrice
})

const stockClasses = computed(() => {
  if (!props.inStock) {
    return 'bg-red-100 text-red-800'
  }
  if (props.stockCount && props.stockCount < 5) {
    return 'bg-yellow-100 text-yellow-800'
  }
  return 'bg-green-100 text-green-800'
})

const stockIcon = computed(() => {
  if (!props.inStock) return 'heroicons:x-circle'
  if (props.stockCount && props.stockCount < 5) return 'heroicons:exclamation-triangle'
  return 'heroicons:check-circle'
})

const stockText = computed(() => {
  if (!props.inStock) return 'Out of Stock'
  if (props.stockCount && props.stockCount < 5) return `Only ${props.stockCount} left`
  return 'In Stock'
})

// Event handlers
const handleClick = () => {
  if (props.clickable) {
    emit('click')
  }
}

const handlePrimaryAction = () => {
  emit('primaryAction')
}

const handleSecondaryAction = () => {
  emit('secondaryAction')
}

const handleWishlist = () => {
  emit('wishlist')
}

const handleShare = () => {
  emit('share')
}

const handleCompare = () => {
  emit('compare')
}
</script>
